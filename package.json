{"name": "vela-sweets-diagnostic-system", "version": "1.0.0", "description": "نظام فحص ذكي وتشخيص متقدم لمشروع VelaSweets", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "lucide-react": "^0.294.0", "clsx": "^2.0.0"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "vite": "^5.0.8", "vitest": "^1.0.4"}, "keywords": ["diagnostic", "testing", "analysis", "vela-sweets", "arabic", "react"], "author": "VelaSweets Team", "license": "MIT"}