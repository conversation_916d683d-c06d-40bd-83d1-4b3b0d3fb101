@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  html[dir="rtl"] {
    font-family: '<PERSON><PERSON><PERSON>', 'Noto Sans Arabic', Arial, sans-serif;
  }
  
  html[dir="ltr"] {
    font-family: 'Inter', system-ui, sans-serif;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
  }
  
  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 active:bg-primary-800;
  }
  
  .btn-secondary {
    @apply bg-gray-100 text-gray-900 hover:bg-gray-200 active:bg-gray-300;
  }
  
  .btn-success {
    @apply bg-success-600 text-white hover:bg-success-700 active:bg-success-800;
  }
  
  .btn-danger {
    @apply bg-danger-600 text-white hover:bg-danger-700 active:bg-danger-800;
  }
  
  .btn-warning {
    @apply bg-warning-600 text-white hover:bg-warning-700 active:bg-warning-800;
  }
  
  .btn-info {
    @apply bg-info-600 text-white hover:bg-info-700 active:bg-info-800;
  }
  
  .card {
    @apply bg-white rounded-lg border border-gray-200 shadow-sm;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-gray-200;
  }
  
  .card-body {
    @apply px-6 py-4;
  }
  
  .card-footer {
    @apply px-6 py-4 border-t border-gray-200;
  }
  
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-success {
    @apply bg-success-100 text-success-800;
  }
  
  .badge-danger {
    @apply bg-danger-100 text-danger-800;
  }
  
  .badge-warning {
    @apply bg-warning-100 text-warning-800;
  }
  
  .badge-info {
    @apply bg-info-100 text-info-800;
  }
  
  .progress-bar {
    @apply w-full bg-gray-200 rounded-full h-2.5;
  }
  
  .progress-fill {
    @apply h-2.5 rounded-full transition-all duration-300 ease-in-out;
  }
}
