export const en = {
  // Navigation
  nav: {
    dashboard: 'Dashboard',
    diagnostics: 'Diagnostics',
    reports: 'Reports',
    settings: 'Settings',
    language: 'Language'
  },
  
  // Dashboard
  dashboard: {
    title: 'Smart Diagnostic & Advanced Analysis System',
    subtitle: 'AI-powered system health monitoring and analysis',
    startDiagnostic: 'Start Comprehensive Scan',
    quickScan: 'Quick Scan',
    fullScan: 'Full Scan',
    lastScan: 'Last Scan',
    systemHealth: 'System Health',
    totalIssues: 'Total Issues',
    criticalIssues: 'Critical Issues',
    resolvedIssues: 'Resolved Issues',
    successRate: 'Success Rate'
  },
  
  // Diagnostic System
  diagnostic: {
    title: 'Advanced Diagnostic System',
    scanning: 'Scanning...',
    analyzing: 'Analyzing...',
    generating: 'Generating Report...',
    completed: 'Scan Completed',
    categories: {
      currency: 'Currency System',
      ui: 'User Interface',
      auth: 'Authentication',
      language: 'Language Support',
      products: 'Product Catalog',
      cart: 'Shopping Cart',
      security: 'Security',
      performance: 'Performance',
      database: 'Database',
      api: 'API'
    },
    status: {
      passed: 'Passed',
      failed: 'Failed',
      warning: 'Warning',
      info: 'Info',
      pending: 'Pending',
      running: 'Running'
    },
    priority: {
      critical: 'Critical',
      high: 'High',
      medium: 'Medium',
      low: 'Low'
    }
  },
  
  // Analysis
  analysis: {
    title: 'Root Cause Analysis',
    rootCause: 'Root Cause',
    impact: 'Impact',
    solution: 'Proposed Solution',
    steps: 'Fix Steps',
    category: 'Category',
    priority: 'Priority',
    estimatedTime: 'Estimated Time',
    difficulty: 'Difficulty Level',
    dependencies: 'Dependencies'
  },
  
  // Reports
  reports: {
    title: 'Diagnostic Reports',
    export: 'Export Report',
    share: 'Share',
    copy: 'Copy',
    download: 'Download',
    summary: 'Report Summary',
    details: 'Details',
    recommendations: 'Recommendations',
    actionPlan: 'Action Plan'
  },
  
  // Common
  common: {
    loading: 'Loading...',
    error: 'Error',
    success: 'Success',
    warning: 'Warning',
    info: 'Info',
    retry: 'Retry',
    cancel: 'Cancel',
    save: 'Save',
    close: 'Close',
    back: 'Back',
    next: 'Next',
    previous: 'Previous',
    refresh: 'Refresh',
    search: 'Search',
    filter: 'Filter',
    sort: 'Sort',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    add: 'Add',
    update: 'Update',
    confirm: 'Confirm',
    yes: 'Yes',
    no: 'No'
  },
  
  // Currency
  currency: {
    iqd: 'Iraqi Dinar',
    symbol: 'IQD'
  },
  
  // Time
  time: {
    now: 'Now',
    minute: 'minute',
    minutes: 'minutes',
    hour: 'hour',
    hours: 'hours',
    day: 'day',
    days: 'days',
    week: 'week',
    weeks: 'weeks',
    month: 'month',
    months: 'months',
    year: 'year',
    years: 'years'
  }
};
