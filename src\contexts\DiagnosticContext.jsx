import React, { createContext, useContext, useState, useCallback } from 'react';

const DiagnosticContext = createContext();

export const useDiagnostic = () => {
  const context = useContext(DiagnosticContext);
  if (!context) {
    throw new Error('useDiagnostic must be used within a DiagnosticProvider');
  }
  return context;
};

export const DiagnosticProvider = ({ children }) => {
  const [isScanning, setIsScanning] = useState(false);
  const [scanProgress, setScanProgress] = useState(0);
  const [currentScanStep, setCurrentScanStep] = useState('');
  const [scanResults, setScanResults] = useState(null);
  const [scanHistory, setScanHistory] = useState([]);

  // Diagnostic test categories and their tests
  const diagnosticCategories = {
    currency: {
      name: 'نظام العملة',
      tests: [
        'currency_format_validation',
        'iqd_symbol_display',
        'price_calculation',
        'shipping_cost_calculation'
      ]
    },
    ui: {
      name: 'واجهة المستخدم',
      tests: [
        'responsive_design',
        'rtl_ltr_support',
        'font_loading',
        'color_contrast',
        'accessibility'
      ]
    },
    auth: {
      name: 'المصادقة',
      tests: [
        'login_functionality',
        'registration_validation',
        'password_security',
        'session_management'
      ]
    },
    language: {
      name: 'دعم اللغات',
      tests: [
        'arabic_support',
        'kurdish_support',
        'english_support',
        'direction_switching',
        'translation_completeness'
      ]
    },
    products: {
      name: 'كتالوج المنتجات',
      tests: [
        'product_display',
        'search_functionality',
        'filtering_system',
        'product_details'
      ]
    },
    cart: {
      name: 'سلة التسوق',
      tests: [
        'add_to_cart',
        'cart_persistence',
        'quantity_management',
        'checkout_process'
      ]
    },
    security: {
      name: 'الأمان',
      tests: [
        'input_validation',
        'xss_protection',
        'csrf_protection',
        'data_encryption'
      ]
    },
    performance: {
      name: 'الأداء',
      tests: [
        'page_load_speed',
        'image_optimization',
        'bundle_size',
        'memory_usage'
      ]
    }
  };

  // Simulate diagnostic tests
  const runDiagnosticTest = async (category, testName) => {
    // Simulate test execution time
    await new Promise(resolve => setTimeout(resolve, Math.random() * 2000 + 500));
    
    // Simulate test results with realistic scenarios
    const testResults = {
      // Currency tests
      currency_format_validation: { status: 'passed', score: 95 },
      iqd_symbol_display: { status: 'passed', score: 100 },
      price_calculation: { status: 'warning', score: 85, issue: 'تقريب الأسعار قد يحتاج تحسين' },
      shipping_cost_calculation: { status: 'passed', score: 100 },
      
      // UI tests
      responsive_design: { status: 'warning', score: 80, issue: 'بعض العناصر تحتاج تحسين على الشاشات الصغيرة' },
      rtl_ltr_support: { status: 'passed', score: 90 },
      font_loading: { status: 'failed', score: 60, issue: 'خطوط الويب تستغرق وقت طويل للتحميل' },
      color_contrast: { status: 'passed', score: 95 },
      accessibility: { status: 'warning', score: 75, issue: 'بعض العناصر تفتقر لخصائص الوصول' },
      
      // Auth tests
      login_functionality: { status: 'passed', score: 100 },
      registration_validation: { status: 'warning', score: 85, issue: 'التحقق من رقم الهاتف يحتاج تحسين' },
      password_security: { status: 'passed', score: 90 },
      session_management: { status: 'passed', score: 95 },
      
      // Language tests
      arabic_support: { status: 'passed', score: 100 },
      kurdish_support: { status: 'warning', score: 80, issue: 'بعض الترجمات الكردية ناقصة' },
      english_support: { status: 'passed', score: 95 },
      direction_switching: { status: 'passed', score: 100 },
      translation_completeness: { status: 'warning', score: 85, issue: 'بعض النصوص غير مترجمة' },
      
      // Products tests
      product_display: { status: 'passed', score: 90 },
      search_functionality: { status: 'failed', score: 50, issue: 'البحث لا يدعم النص العربي بشكل كامل' },
      filtering_system: { status: 'warning', score: 75, issue: 'الفلاتر تحتاج تحسين' },
      product_details: { status: 'passed', score: 95 },
      
      // Cart tests
      add_to_cart: { status: 'passed', score: 100 },
      cart_persistence: { status: 'warning', score: 80, issue: 'السلة لا تحفظ البيانات عند إغلاق المتصفح' },
      quantity_management: { status: 'passed', score: 90 },
      checkout_process: { status: 'failed', score: 40, issue: 'عملية الدفع تحتاج إعادة تصميم' },
      
      // Security tests
      input_validation: { status: 'warning', score: 75, issue: 'بعض الحقول تحتاج تحقق إضافي' },
      xss_protection: { status: 'passed', score: 95 },
      csrf_protection: { status: 'passed', score: 90 },
      data_encryption: { status: 'passed', score: 100 },
      
      // Performance tests
      page_load_speed: { status: 'warning', score: 70, issue: 'سرعة التحميل تحتاج تحسين' },
      image_optimization: { status: 'failed', score: 45, issue: 'الصور غير محسنة للويب' },
      bundle_size: { status: 'warning', score: 80, issue: 'حجم الملفات كبير نسبياً' },
      memory_usage: { status: 'passed', score: 85 }
    };
    
    return testResults[testName] || { status: 'passed', score: 100 };
  };

  const startDiagnostic = useCallback(async (scanType = 'full') => {
    setIsScanning(true);
    setScanProgress(0);
    setCurrentScanStep('بدء الفحص...');
    
    const results = {
      id: Date.now(),
      timestamp: new Date().toISOString(),
      type: scanType,
      categories: {},
      summary: {
        totalTests: 0,
        passedTests: 0,
        failedTests: 0,
        warningTests: 0,
        overallScore: 0
      }
    };
    
    const categoriesToScan = scanType === 'quick' 
      ? ['currency', 'ui', 'auth'] 
      : Object.keys(diagnosticCategories);
    
    let totalTests = 0;
    let completedTests = 0;
    
    // Calculate total tests
    categoriesToScan.forEach(categoryKey => {
      totalTests += diagnosticCategories[categoryKey].tests.length;
    });
    
    results.summary.totalTests = totalTests;
    
    // Run tests for each category
    for (const categoryKey of categoriesToScan) {
      const category = diagnosticCategories[categoryKey];
      setCurrentScanStep(`فحص ${category.name}...`);
      
      const categoryResults = {
        name: category.name,
        tests: {},
        summary: {
          totalTests: category.tests.length,
          passedTests: 0,
          failedTests: 0,
          warningTests: 0,
          averageScore: 0
        }
      };
      
      let categoryTotalScore = 0;
      
      // Run each test in the category
      for (const testName of category.tests) {
        const testResult = await runDiagnosticTest(categoryKey, testName);
        categoryResults.tests[testName] = testResult;
        categoryTotalScore += testResult.score;
        
        // Update counters
        if (testResult.status === 'passed') {
          categoryResults.summary.passedTests++;
          results.summary.passedTests++;
        } else if (testResult.status === 'failed') {
          categoryResults.summary.failedTests++;
          results.summary.failedTests++;
        } else if (testResult.status === 'warning') {
          categoryResults.summary.warningTests++;
          results.summary.warningTests++;
        }
        
        completedTests++;
        setScanProgress((completedTests / totalTests) * 100);
      }
      
      categoryResults.summary.averageScore = Math.round(categoryTotalScore / category.tests.length);
      results.categories[categoryKey] = categoryResults;
    }
    
    // Calculate overall score
    const totalScore = Object.values(results.categories).reduce((sum, cat) => sum + cat.summary.averageScore, 0);
    results.summary.overallScore = Math.round(totalScore / Object.keys(results.categories).length);
    
    setCurrentScanStep('إنشاء التقرير...');
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    setScanResults(results);
    setScanHistory(prev => [results, ...prev.slice(0, 9)]); // Keep last 10 scans
    setIsScanning(false);
    setScanProgress(100);
    setCurrentScanStep('اكتمل الفحص');
  }, []);

  const value = {
    isScanning,
    scanProgress,
    currentScanStep,
    scanResults,
    scanHistory,
    diagnosticCategories,
    startDiagnostic
  };

  return (
    <DiagnosticContext.Provider value={value}>
      {children}
    </DiagnosticContext.Provider>
  );
};
