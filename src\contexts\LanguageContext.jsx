import React, { createContext, useContext, useState, useEffect } from 'react';
import { ar } from '../locales/ar';
import { en } from '../locales/en';
import { ku } from '../locales/ku';

const translations = {
  ar,
  en,
  ku
};

const LanguageContext = createContext();

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

export const LanguageProvider = ({ children }) => {
  const [currentLanguage, setCurrentLanguage] = useState('ar');
  const [direction, setDirection] = useState('rtl');

  useEffect(() => {
    // Load saved language from localStorage
    const savedLanguage = localStorage.getItem('vela-language') || 'ar';
    setCurrentLanguage(savedLanguage);
    updateDirection(savedLanguage);
  }, []);

  const updateDirection = (lang) => {
    const newDirection = lang === 'en' ? 'ltr' : 'rtl';
    setDirection(newDirection);
    
    // Update HTML attributes
    document.documentElement.setAttribute('lang', lang);
    document.documentElement.setAttribute('dir', newDirection);
    
    // Update body class for font family
    document.body.className = `font-${lang === 'en' ? 'english' : lang === 'ku' ? 'kurdish' : 'arabic'}`;
  };

  const changeLanguage = (newLanguage) => {
    setCurrentLanguage(newLanguage);
    updateDirection(newLanguage);
    localStorage.setItem('vela-language', newLanguage);
  };

  const t = (key) => {
    const keys = key.split('.');
    let value = translations[currentLanguage];
    
    for (const k of keys) {
      if (value && typeof value === 'object') {
        value = value[k];
      } else {
        // Fallback to Arabic if key not found
        value = translations.ar;
        for (const fallbackKey of keys) {
          if (value && typeof value === 'object') {
            value = value[fallbackKey];
          } else {
            return key; // Return key if not found in fallback
          }
        }
        break;
      }
    }
    
    return value || key;
  };

  const formatCurrency = (amount) => {
    const symbol = t('currency.symbol');
    const formattedAmount = new Intl.NumberFormat(currentLanguage === 'en' ? 'en-US' : 'ar-IQ').format(amount);
    return direction === 'rtl' ? `${formattedAmount} ${symbol}` : `${symbol} ${formattedAmount}`;
  };

  const formatDate = (date) => {
    const options = {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };
    
    const locale = currentLanguage === 'en' ? 'en-US' : currentLanguage === 'ku' ? 'ku-IQ' : 'ar-IQ';
    return new Intl.DateTimeFormat(locale, options).format(new Date(date));
  };

  const getLanguages = () => [
    { code: 'ar', name: 'العربية', nativeName: 'العربية' },
    { code: 'ku', name: 'کوردی', nativeName: 'کوردی' },
    { code: 'en', name: 'English', nativeName: 'English' }
  ];

  const value = {
    currentLanguage,
    direction,
    changeLanguage,
    t,
    formatCurrency,
    formatDate,
    getLanguages,
    isRTL: direction === 'rtl'
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};
