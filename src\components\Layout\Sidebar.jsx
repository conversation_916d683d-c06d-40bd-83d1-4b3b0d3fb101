import React from 'react';
import { useLanguage } from '../../contexts/LanguageContext';
import { 
  LayoutDashboard, 
  Stethoscope, 
  FileText, 
  Settings,
  TrendingUp,
  Shield,
  Database,
  Zap
} from 'lucide-react';

const Sidebar = ({ activeTab, setActiveTab }) => {
  const { t, isRTL } = useLanguage();

  const menuItems = [
    {
      id: 'dashboard',
      label: t('nav.dashboard'),
      icon: LayoutDashboard,
      color: 'text-blue-600'
    },
    {
      id: 'diagnostics',
      label: t('nav.diagnostics'),
      icon: Stethoscope,
      color: 'text-green-600'
    },
    {
      id: 'reports',
      label: t('nav.reports'),
      icon: FileText,
      color: 'text-purple-600'
    },
    {
      id: 'analytics',
      label: 'التحليلات',
      icon: TrendingUp,
      color: 'text-orange-600'
    },
    {
      id: 'security',
      label: 'الأمان',
      icon: Shield,
      color: 'text-red-600'
    },
    {
      id: 'performance',
      label: 'الأداء',
      icon: Zap,
      color: 'text-yellow-600'
    },
    {
      id: 'database',
      label: 'قاعدة البيانات',
      icon: Database,
      color: 'text-indigo-600'
    },
    {
      id: 'settings',
      label: t('nav.settings'),
      icon: Settings,
      color: 'text-gray-600'
    }
  ];

  return (
    <aside className="w-64 bg-white shadow-sm border-r border-gray-200 h-full">
      <nav className="mt-8">
        <div className="px-4">
          <h2 className="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-4">
            القائمة الرئيسية
          </h2>
        </div>
        <div className="space-y-1 px-2">
          {menuItems.map((item) => {
            const Icon = item.icon;
            const isActive = activeTab === item.id;
            
            return (
              <button
                key={item.id}
                onClick={() => setActiveTab(item.id)}
                className={`
                  w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors
                  ${isActive 
                    ? 'bg-primary-50 text-primary-700 border-r-2 border-primary-600' 
                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  }
                  ${isRTL ? 'text-right' : 'text-left'}
                `}
              >
                <Icon className={`
                  w-5 h-5 flex-shrink-0
                  ${isRTL ? 'ml-3' : 'mr-3'}
                  ${isActive ? 'text-primary-600' : item.color}
                `} />
                {item.label}
              </button>
            );
          })}
        </div>
      </nav>
    </aside>
  );
};

export default Sidebar;
